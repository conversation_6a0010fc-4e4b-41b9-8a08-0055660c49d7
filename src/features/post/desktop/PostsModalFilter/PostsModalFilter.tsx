import { useModal } from '@payloadcms/ui'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import FilterIcon from '@/assets/icons/setting-4.svg'

const PostsModalFilter: React.FC = () => {
  const { isModalOpen, openModal, closeModal } = useModal()
  const t = useTranslations()
  return (
    <>
      <div className="typo-body-6 flex cursor-pointer items-center gap-3 text-primary-500">
        {t('MES-481')}
        <Image alt="filter" src={FilterIcon} width={16} height={16} />
      </div>


      <Modal></Modal>
    </>
  )
}

export default PostsModalFilter
